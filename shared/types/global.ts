// shared/types/global.d.ts
import React from 'react';
// Common interfaces used across the application

export interface BaseComponent {
  className?: string;
  children?: React.ReactNode;
}

export interface ApiResponse<T = any> {
  success: number;
  message: string;
  data?: T;
  count?: number;
}

// Comprehensive User interface that consolidates all user properties used across the application
export interface User {
  id: number;
  first_name: string;
  last_name: string;
  phone: string;
  phone_verified: boolean;
  parent_type: string;
  parent_id: number;
  email: string;
  reset_password_token: string | null;
  reset_password_sent_at: string | null;
  remember_created_at: string | null;
  confirmation_token: string | null;
  confirmed_at: string | null;
  confirmation_sent_at: string | null;
  unconfirmed_email: string | null;
  created_at: string;
  updated_at: string;
  tenant_id?: number;
  agent_name: string;
  active: boolean;
  deactivated_by_id: number | null;
  deactivated_by_type: string | null;
  deactivated_at: string | null;
  allowed_currencies: string;
  kyc_regulated: boolean;
  login_count: number;
  secret_key: string | null;
  affiliate_token?: string;
  ip_whitelist: string;
  is_applied_ip_whitelist: boolean;
  ip_whitelist_type: string;
  timezone?: string;
  agent_type: number;
  agent_action: string;
  affiliate_status: boolean;
  reporting_email: string | null;
  reporting_email_verified: boolean;
  roles: string[];
  internalPlayerID: number;
}

export interface MenuItem {
  id: number;
  title: string;
  path?: string;
  icon?: React.ReactNode;
  type: 'link' | 'sub' | 'empty';
  menutitle?: string;
  selected?: boolean;
  active?: boolean;
  badgetxt?: string;
  class?: string;
  children?: MenuItem[];
  dirchange?: boolean;
  [key: string]: any;
}

export interface ThemeConfig {
  lang: string;
  dir: 'ltr' | 'rtl';
  class: 'light' | 'dark';
  dataMenuStyles: string;
  dataNavLayout: 'vertical' | 'horizontal';
  dataHeaderStyles: string;
  dataVerticalStyle: string;
  toggled: string;
  dataNavStyle: string;
  dataPageStyle: string;
  dataWidth: string;
  dataMenuPosition: string;
  dataHeaderPosition: string;
  loader: string;
  iconOverlay: string;
  colorPrimaryRgb: string;
  PrimaryRgb: string;
  bodyBg: string;
  darkBg: string;
  inputBorder: string;
  lightRgb: string;
  gray: string;
  bgImg: string;
  iconText: string;
}

export interface LoadingState {
  isLoading: boolean;
  error?: string | null;
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
}

// Utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
