// shared/providers/TurboStarsWebSocketProvider.tsx
"use client";

import React, { createContext, useContext, useEffect, useRef } from "react";
import { useTurboStarsWebSocket } from "@/shared/hooks";
import { useSportsbookStore } from "@/shared/stores/sportsbookStore";
import { useAuthStore } from "@/shared/stores/authStore";
import { turboStarsWebSocketService } from "@/shared/services/webSocketService";
import { CashierTurboPlaceBetDetails } from "@/shared/types/user-management-types";

interface TurboStarsWebSocketContextType {
  isConnected: boolean;
  connectionState: string;
  lastNotification: CashierTurboPlaceBetDetails | null;
  error: string | null;
  consecutiveFailures: number;
  hasReachedMaxFailures: boolean;
  maxConsecutiveFailures: number;
}

const TurboStarsWebSocketContext = createContext<TurboStarsWebSocketContextType | null>(null);

interface TurboStarsWebSocketProviderProps {
  children: React.ReactNode;
  autoConnect?: boolean;
}

export const TurboStarsWebSocketProvider: React.FC<TurboStarsWebSocketProviderProps> = ({
  children,
  autoConnect: _autoConnect = false // We manage connection based on sportsbook state
}) => {
  const {
    isOpen: isSportsbookOpen,
    setWebSocketConnected,
    addBetNotification,
    showBetPopup,
    setCurrentUserId,
    currentUserId
  } = useSportsbookStore();

  // Get current user and token from auth store
  const { user, token } = useAuthStore();

  // Register this provider with the WebSocket service
  useEffect(() => {
    turboStarsWebSocketService.registerProvider();
    return () => {
      turboStarsWebSocketService.unregisterProvider();
    };
  }, []);

  const hasInitialized = useRef(false);
  const lastConnectionAttempt = useRef<number>(0);

  // Track consecutive failed connection attempts
  const consecutiveFailures = useRef<number>(0);
  const maxConsecutiveFailures = 2; // Stop trying after 2 consecutive failures
  const hasReachedMaxFailures = useRef<boolean>(false);

  /**
   * Handle bet placement notifications with user context validation
   * Only shows bet details popup if the notification is for the current user
   * @param notification - The bet notification details from WebSocket
   */
  const handleBetPlaced = (notification: CashierTurboPlaceBetDetails) => {

    // Validate user context - use internalPlayerID as primary identifier
    const internalPlayerID = user?.internalPlayerID;

    // Only show bet details popup if the notification is for the current user
    if (internalPlayerID) {
      showBetPopup(notification);

    }

    // Still update the general notification store for backward compatibility
    addBetNotification(notification);
  };

  // Initialize WebSocket connection without auto-connect (we'll manage connection manually)
  const {
    isConnected,
    connectionState,
    lastNotification,
    error,
    connect,
    disconnect
  } = useTurboStarsWebSocket({
    autoConnect: false, // We manage connection based on sportsbook state
    onBetPlaced: handleBetPlaced
  });

  // Set current user ID in store when user changes (fallback only if not already set)
  useEffect(() => {
    if (user?.internalPlayerID && !currentUserId) {
      setCurrentUserId(Number(user.internalPlayerID));
    }
  }, [user?.internalPlayerID, currentUserId, setCurrentUserId]);

  // Monitor sportsbook state and manage WebSocket connection accordingly
  useEffect(() => {
    if (isSportsbookOpen && !isConnected && !hasReachedMaxFailures.current && user?.internalPlayerID) {
      // Sportsbook opened - connect WebSocket if not already connected and haven't reached max failures
      lastConnectionAttempt.current = Date.now();

      // Use internalPlayerID as the primary user identifier
      const activeUserId = user.internalPlayerID;

      if (!activeUserId) {
        return;
      }

      // Token authentication is commented out - using internalPlayerID instead
      // if (!token) {
      //   console.error("No authentication token available for WebSocket connection");
      //   return;
      // }

      const wsUrl = "wss://api.ingrandstation.com/graphql";
      const config = {
        url: wsUrl,
        channel: "CashierTurboPlaceBetDetails",
        userId: activeUserId.toString(), // Convert internalPlayerID to string for WebSocket
        token: token || "" // Provide empty string as fallback (token auth is commented out)
      };

      connect(config).catch(err => {
        // eslint-disable-next-line no-console
        console.warn("Failed to connect WebSocket when sportsbook opened:", err);

        // Increment failure counter
        consecutiveFailures.current += 1;

        // Check if we've reached the maximum number of failures
        if (consecutiveFailures.current >= maxConsecutiveFailures) {
          hasReachedMaxFailures.current = true;
        }
      });
    } else if (isSportsbookOpen && !isConnected && hasReachedMaxFailures.current) {
      // Log that we're not attempting to connect due to max failures reached
      // eslint-disable-next-line no-console
      console.info("WebSocket connection not attempted - maximum consecutive failures reached. Close and reopen sportsbook to retry.");
    }

    // Note: We maintain persistent WebSocket connections even when sportsbook closes
    // This prevents unnecessary disconnection/reconnection cycles and ensures
    // the singleton connection remains available for immediate reuse
  }, [isSportsbookOpen, isConnected, token, connect, disconnect, user?.internalPlayerID, currentUserId]); // Removed token dependency to prevent excessive re-renders

  // Reset failure counter when sportsbook is reopened after being closed
  useEffect(() => {
    if (isSportsbookOpen && hasReachedMaxFailures.current) {
      consecutiveFailures.current = 0;
      hasReachedMaxFailures.current = false;
    }
  }, [isSportsbookOpen]);

  // Update last connection attempt timestamp when we try to connect
  useEffect(() => {
    if (isSportsbookOpen && !hasInitialized.current) {
      lastConnectionAttempt.current = Date.now();
    }
  }, [isSportsbookOpen]);

  // Update store with connection status and reset failure counter on successful connection
  useEffect(() => {

    setWebSocketConnected(isConnected);

    // Reset failure counter when successfully connected
    if (isConnected) {
      if (consecutiveFailures.current > 0) {
        consecutiveFailures.current = 0;
        hasReachedMaxFailures.current = false;
      }
    }
  }, [isConnected, setWebSocketConnected, isSportsbookOpen, currentUserId, user?.id]);

  // Log connection state changes
  useEffect(() => {
    if (!hasInitialized.current) {
      hasInitialized.current = true;
    }
  }, [connectionState]);

  // Log errors and track failures (but don't treat WebSocket unavailability as a critical error)
  const lastLoggedError = useRef<string | null>(null);
  useEffect(() => {
    if (error && error !== lastLoggedError.current) {
      lastLoggedError.current = error;

      if (error.includes("WebSocket server not available")) {
        // Count server unavailable as a failure for retry limiting
        if (isSportsbookOpen && !isConnected) {
          consecutiveFailures.current += 1;
          if (consecutiveFailures.current >= maxConsecutiveFailures) {
            hasReachedMaxFailures.current = true;
          }
        }
      } else {
        // Count other errors as failures too
        if (isSportsbookOpen && !isConnected) {
          consecutiveFailures.current += 1;
          if (consecutiveFailures.current >= maxConsecutiveFailures) {
            hasReachedMaxFailures.current = true;
          }
        }
      }

      // Update cooldown on error to prevent immediate retry
      lastConnectionAttempt.current = Date.now();
    }
  }, [error, isSportsbookOpen, isConnected]);

  const contextValue: TurboStarsWebSocketContextType = {
    isConnected,
    connectionState,
    lastNotification,
    error,
    consecutiveFailures: consecutiveFailures.current,
    hasReachedMaxFailures: hasReachedMaxFailures.current,
    maxConsecutiveFailures
  };

  return (
    <TurboStarsWebSocketContext.Provider value={contextValue}>
      {children}
    </TurboStarsWebSocketContext.Provider>
  );
};

// Hook to use the WebSocket context
export const useTurboStarsWebSocketContext = (): TurboStarsWebSocketContextType => {
  const context = useContext(TurboStarsWebSocketContext);

  if (!context) {
    throw new Error(
      "useTurboStarsWebSocketContext must be used within a TurboStarsWebSocketProvider"
    );
  }

  return context;
};

// Connection status indicator component
interface WebSocketStatusIndicatorProps {
  className?: string;
  showText?: boolean;
}

export const WebSocketStatusIndicator: React.FC<WebSocketStatusIndicatorProps> = ({
  className = "",
  showText = false
}) => {
  const { isConnected, connectionState, error, hasReachedMaxFailures, consecutiveFailures, maxConsecutiveFailures } = useTurboStarsWebSocketContext();

  const getStatusColor = () => {
    if (hasReachedMaxFailures) return "text-red-600";
    if (error && !error.includes("WebSocket server not available")) return "text-red-500";
    if (error && error.includes("WebSocket server not available")) return "text-gray-400";
    if (isConnected) return "text-green-500";
    if (connectionState === "CONNECTING") return "text-yellow-500";
    return "text-gray-400";
  };

  const getStatusText = () => {
    if (hasReachedMaxFailures) return `Failed (${consecutiveFailures}/${maxConsecutiveFailures})`;
    if (error && error.includes("WebSocket server not available")) return "Offline";
    if (error) return "Error";
    if (isConnected) return "Connected";
    if (connectionState === "CONNECTING") return "Connecting";
    return "Disconnected";
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div className={`w-2 h-2 rounded-full ${getStatusColor().replace("text-", "bg-")} ${isConnected ? "animate-pulse" : ""}`} />
      {showText && (
        <span className={`text-xs font-medium ${getStatusColor()}`}>
          {getStatusText()}
        </span>
      )}
    </div>
  );
};
