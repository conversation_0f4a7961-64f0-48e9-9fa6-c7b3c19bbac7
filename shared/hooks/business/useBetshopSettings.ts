// shared/hooks/business/useBetshopSettings.ts
import { useAuthStore } from '@/shared/stores/authStore';

interface BetshopSettingsRequest {
  tenantID: number;
}

interface BetshopSettingsItem {
  id: string;
  key: string;
  value: string;
  description?: string;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
}

interface BetshopSettingsResponse {
  success: number;
  message: string;
  data: {
    settingsDetails: BetshopSettingsItem[];
  };
  errors?: string[];
}

interface BetshopSettingsError {
  success: number;
  message: string;
  errors?: string[];
}

/**
 * Fetch betshop settings from the reporting API
 */
export const fetchBetshopSettings = async (tenantId: number): Promise<BetshopSettingsResponse> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  if (!tenantId) {
    throw new Error('Tenant ID is required');
  }

  // Use the reporting API URL
  const baseUrl = process.env.NEXT_PUBLIC_REPORTING_BACKEND_URL || 'https://reporting.ingrandstation.com';

  const requestBody: BetshopSettingsRequest = {
    tenantID: tenantId
  };

  const response = await fetch(`${baseUrl}/api/v2/admin/betshop-settings`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    const errorData: BetshopSettingsError = await response.json();
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  const data: BetshopSettingsResponse = await response.json();
  if (data?.data?.settingsDetails?.length <= 0) {
    throw new Error(data.message || 'Failed to fetch betshop settings');
  }

  return data;
};

/**
 * Utility function to fetch betshop settings (not a React hook)
 */
export const getBetshopSettings = async (tenantId?: number): Promise<BetshopSettingsResponse | undefined> => {
  const { user, token } = useAuthStore.getState();
  const effectiveTenantId = tenantId || user?.tenant_id;

  if (!token) {
    throw new Error('Authentication token is required');
  }
  if (!effectiveTenantId) {
    // No tenantId, return undefined
    return undefined;
  }
  return await fetchBetshopSettings(effectiveTenantId);
};

