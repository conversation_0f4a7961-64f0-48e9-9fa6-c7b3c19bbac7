// shared/utils/transformUserData.ts - Utility functions for transforming user data
import { UserDetailsData } from '@/shared/types/user-management-types';
import { EditUserData } from '@/shared/query';
import { User } from '@/shared/types/global';

/**
 * IMPORTANT: This project uses three different user data structures:
 *
 * 1. User (from global.ts) - Authenticated user data (snake_case: first_name, last_name, phone_verified, agent_name)
 *    Used in: auth store, profile components, header components
 *
 * 2. UserData (from user-management-types.ts) - User list data (camelCase: firstName, lastName, phoneVerified)
 *    Used in: user management table, user list queries
 *
 * 3. UserDetailsData (from user-management-types.ts) - Detailed user data (camelCase: firstName, lastName, phoneVerified)
 *    Used in: user details pages, user edit forms, user details queries
 *
 * Always use the correct interface for the context you're working in!
 */

/**
 * Transforms UserDetailsData to EditUserData format for form editing
 * This function maps the user details response to the format expected by the edit user form
 * 
 * @param userData - User details data from the API
 * @returns EditUserData formatted for form editing
 */
export const transformUserDataForEdit = (userData: UserDetailsData): EditUserData => {
  return {
    id: Number(userData.id),
    userName: userData.userName || '',
    nickName: '', // Not available in UserDetailsData
    firstName: userData.firstName || '',
    lastName: userData.lastName || '',
    email: userData.email || '',
    phone: userData.phone || '',
    phoneCode: userData.phoneCode || '+94',
    zipCode: '', // Not available in UserDetailsData
    dateOfBirth: '', // Not available in UserDetailsData
    countryCode: 'LK', // Default value
    currencyId: Number(userData.currencyId) || 1,
    activeBonusId: null, // Not available in UserDetailsData
    vipLevel: Number(userData.playerCategoryLevel) || 1,
    city: '', // Not available in UserDetailsData
    emailVerified: userData.emailVerified || false,
    phoneVerified: userData.phoneVerified || false,
    forceResetPassword: userData.forceResetPassword || false,
    markAsBot: false, // Not available in UserDetailsData
    active: userData.active !== false,
    demo: userData.demo || false,
    affiliatedData: '', // Not available in UserDetailsData
    nationalId: null, // Not available in UserDetailsData
    clickId: null, // Not available in UserDetailsData
    wyntaClickId: null, // Not available in UserDetailsData
    categoryType: null, // Not available in UserDetailsData
    userType: userData.userType || 1,
    encryptedPassword: '', // Don't pre-fill password for edit
    rfidToken: (userData as any).rfidToken || '', // Not available in UserDetailsData yet
  };
};
