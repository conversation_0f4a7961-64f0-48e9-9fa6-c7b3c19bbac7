// shared/query/useAdminWalletQuery.ts
import { useQuery } from '@tanstack/react-query';
import { useAuthStore } from '@/shared/stores/authStore';
import { AdminWalletResponse } from '@/shared/types/user-management-types';
import { checkAndHandle401 } from '@/shared/utils/globalApiErrorHandler';

export interface WalletResponse {
  id: number;
  amount: string;
  primary: boolean;
  currency_id: number;
  owner_type: string;
  owner_id: number;
  created_at: string;
  updated_at: string;
  non_cash_amount: string;
  withdrawal_amount: string;
  one_time_bonus_amount: string;
  sports_freebet_amount: string;
  currency_name: string;
}

/**
 * Fetches admin/cashier wallet data from the API
 * This replaces the complex admin profile query system with a direct wallet API call
 */
const fetchAdminWallet = async (): Promise<AdminWalletResponse> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  // Get the admin backend URL
  const baseUrl = process.env.NEXT_PUBLIC_ADMIN_BACKEND_URL;

  if (!baseUrl) {
    throw new Error('Admin backend URL is not configured');
  }

  const response = await fetch(`${baseUrl}/api/admin/user/wallet`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });

  // Handle 401 errors globally
  await checkAndHandle401(response);

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(
      `Failed to fetch admin wallet data: ${response.status} ${response.statusText}. ${errorText}`
    );
  }

  const data = await response.json();

  return data;
};

/**
 * React Query hook for fetching admin wallet data
 * Returns wallet information including balance and wallet IDs for the current admin/cashier user
 */
export const useAdminWalletQuery = () => {
  const { token, isAuthenticated } = useAuthStore();

  return useQuery({
    queryKey: ['adminWallet'],
    queryFn: fetchAdminWallet,
    enabled: !!token && isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (error.message.includes('401') || error.message.includes('Authentication')) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },

  });
};

/**
 * Helper function to get the primary wallet from admin wallet data
 * Returns the first wallet if no primary wallet is found
 */
export const getPrimaryAdminWallet = (walletData?: AdminWalletResponse): WalletResponse | undefined => {
  if (!walletData?.record || walletData.record.length === 0) {
    return undefined;
  }

  // Find primary wallet first
  const primaryWallet = walletData.record.find(wallet => wallet.primary);

  // If no primary wallet, return the first one
  return primaryWallet || walletData.record[0];
};

/**
 * Helper function to get admin wallet ID for transactions
 * Returns the ID of the primary wallet or first available wallet
 */
export const getAdminWalletId = (walletData?: AdminWalletResponse): string | null => {
  const primaryWallet = getPrimaryAdminWallet(walletData);
  return primaryWallet ? primaryWallet.id.toString() : null;
};
