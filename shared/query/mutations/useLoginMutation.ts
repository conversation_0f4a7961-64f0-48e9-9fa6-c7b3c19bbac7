// shared/query/mutations/useLoginMutation.ts
import { useAuthStore } from '@/shared/stores/authStore'; // Import the auth store
import { getAdminBackendUrl } from '@/shared/utils/envValidation';
import { useMutation, useQueryClient } from '@tanstack/react-query';
// import { User } from '@/shared/types/global';

interface LoginCredentials {
    email: string;
    password: string;
    platformType?: string;
    rememberMe?: boolean;
}

// Extended interface for login response that includes additional admin fields
// interface LoginUser extends User {
// lastName: string; // Required in login response
// phone: string; // Required in login response
// phoneVerified: boolean;
// resetPasswordToken: string | null;
// resetPasswordSentAt: string | null;
// rememberCreatedAt: string | null;
// confirmationToken: string | null;
// confirmedAt: string | null;
// confirmationSentAt: string | null;
// unconfirmedEmail: string | null;
// deactivatedById: number | null;
// deactivatedByType: string | null;
// deactivatedAt: string | null;
// allowedCurrencies: string;
// kycRegulated: boolean;
// loginCount: number;
// secretKey: string | null;
// ipWhitelist: string;
// isAppliedIpWhitelist: boolean;
// ipWhitelistType: string;
// timezone: string;
// agentType: number;
// agentAction: string;
// loginPin: string;
// affiliateStatus: boolean;
// reportingEmail: string | null;
// reportingEmailVerified: boolean;
// }

// interface LoginRecord {
//     loginPinExist: boolean;
//     user: User;
// }

export interface LoginResponse {
    success: number;
    message: string;
    count: number;
    record: {
        loginPinExist?: boolean;
        validPin?: boolean;
        token?: string;
        tokenType?: string;
        token_type?: string;
        user: {
            id: number;
            first_name: string;
            last_name: string;
            phone: string;
            phone_verified: boolean;
            parent_type: string;
            parent_id: number;
            email: string;
            reset_password_token: string | null;
            reset_password_sent_at: string | null;
            remember_created_at: string | null;
            confirmation_token: string | null;
            confirmed_at: string | null;
            confirmation_sent_at: string | null;
            unconfirmed_email: string | null;
            created_at: string;
            updated_at: string;
            tenant_id?: number;
            agent_name: string;
            active: boolean;
            deactivated_by_id: number | null;
            deactivated_by_type: string | null;
            deactivated_at: string | null;
            allowed_currencies: string;
            kyc_regulated: boolean;
            login_count: number;
            secret_key: string | null;
            affiliate_token?: string;
            ip_whitelist: string;
            is_applied_ip_whitelist: boolean;
            ip_whitelist_type: string;
            timezone?: string;
            agent_type: number;
            agent_action: string;
            affiliate_status: boolean;
            reporting_email: string | null;
            reporting_email_verified: boolean;
            roles: string[];
            internalPlayerID: number;
        };
        tenant: {
            id: number;
            domain: string;
            name: string;
            logo_url?: string;
            allowed_modules?: string;
            blog_dashboard_url?: string | null;
            tenant_sports_bet_setting_id?: number;
            menuList?: string[];
            tenant_base_currency?: string;
            global_ip_address?: string;
            tenant_country_code?: string;
            smartico_open_gamification_url?: string | null;
            smartico_gamification_label_key?: string | null;
            smartico_gamification_brand_key?: string | null;
            smartico_gamification_salt_key?: string | null;
            allowed_currencies?: {
                id: number;
                name: string;
                code: string;
                exchange_rate: string;
            }[];
            allowed_languages?: {
                id: number;
                name: string;
                abbreviation: string;
            }[];
            admin_dashboard_auto_logout_duration?: number;
            assigned_providers_values?: string[];
        };
        permissions?: {
            [key: string]: string[] | string;
        };
    };
}

const loginUser = async (credentials: LoginCredentials): Promise<LoginResponse> => {
    // Get validated admin backend URL
    const adminBackendUrl = getAdminBackendUrl();

    const response = await fetch(`${adminBackendUrl}/api/login/admin`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        },
        body: JSON.stringify(credentials),
    });

    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Login failed');
    }

    const responseData = await response.json();
    return responseData;
};

export const useLoginMutation = () => {
    const queryClient = useQueryClient();
    // Get the new setTwoStepRequired and the full setAuth action
    const { setFullAuth, setTwoStepRequired } = useAuthStore();

    return useMutation<LoginResponse, Error, LoginCredentials>({
        mutationFn: loginUser,
        onSuccess: (data) => {

            if (data.success === 1 && data.record?.user) {
                // Check if 2FA is required based on message or loginPinExist field
                const requires2FA = data.record.loginPinExist || data.message?.includes('login pin');

                if (requires2FA) {
                    // User needs to provide a PIN
                    setTwoStepRequired(data.record.user.id, data.record.user.email, data.message);
                } else {
                    // No PIN required, directly authenticate
                    // **IMPORTANT**: Get the actual token from response for full auth
                    const token = data.record.token; // Prioritize `token` field if present, else use `affiliateToken`
                    const user = data.record.user;

                    // Extract tenant information from the response if available
                    if (data.record.tenant && data.record.tenant.id) {
                        (user as any).tenantId = data.record.tenant.id;
                    }

                    if (token) {
                        setFullAuth(token, user); // Use setFullAuth for complete authentication
                    }
                }
                queryClient.invalidateQueries({ queryKey: ['user'] }); // Invalidate user-related queries
            } else {
                throw new Error(data.message || 'Login failed with unexpected response');
            }
        },
        onError: (_error) => {
            // console.error('Login error:', error.message);
            // console.error('Full error object:', error);
            // You might want to clear any partial 2FA state here if a new login attempt fails
            useAuthStore.getState().clearTwoStep();
        },
    });
};
