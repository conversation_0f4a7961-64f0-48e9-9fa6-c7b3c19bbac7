"use client";

import React, { Fragment } from "react";
import { AuthenticationBackgroundWrapper, SportsCategoryCard } from "@/shared/UI/components";
import { useAuthenticationBackground } from "@/shared/hooks/ui/useAuthenticationBackground";
import { useSportsCategoriesQuery } from "@/shared/query/useSportsCategoriesQuery";
import { SportDiscipline } from "@/shared/types/sportsbook-types";
import { useTurboStarsGameLaunchMutation } from "@/shared/query/mutations/useTurboStarsGameLaunchMutation";
import { useAuthStore } from "@/shared/stores/authStore";
import {
  openTurboStarsSportsbookInline,
  validateTurboStarsResponse,
  getTurboStarsErrorMessage,
  isTurboStarsSupported
} from "@/shared/utils/turboStarsUtils";

/**
 * Client-side component for sportsbook category selection
 * Displays sports categories in a grid layout with background animation
 */
export function SportsbookPageClient() {
  // Get background images for authentication wrapper
  const { backgroundImages } = useAuthenticationBackground();

  // Fetch sports categories
  const { data: categoriesData, isLoading, isError, error } = useSportsCategoriesQuery();

  // Get current user for sportsbook session
  const { user } = useAuthStore();

  // TurboStars game launch mutation
  const turboStarsLaunchMutation = useTurboStarsGameLaunchMutation();

  // Handle category selection and launch TurboStars with selected sport
  const handleCategorySelect = async (discipline: SportDiscipline) => {
    // Check if TurboStars is supported
    if (!isTurboStarsSupported()) {
      // eslint-disable-next-line no-console
      console.error('TurboStars sportsbook is not supported in this environment');
      return;
    }

    try {
      const response = await turboStarsLaunchMutation.mutateAsync({
        providerName: 'TurboStars',
        gameName: discipline.id, // Pass the selected sport ID as game name
      });

      // Validate the response
      if (!validateTurboStarsResponse(response)) {
        throw new Error('Invalid response from TurboStars API');
      }

      // Open the sportsbook inline with selected sport
      const userIdentifier = user?.email || user?.first_name || 'current-user';
      openTurboStarsSportsbookInline(response, userIdentifier, user?.id);

    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to launch TurboStars sportsbook:', error);
      const errorMessage = getTurboStarsErrorMessage(error);
      // You could show a toast notification here
      alert(errorMessage);
    }
  };

  return (
    <Fragment>
      <AuthenticationBackgroundWrapper
        backgroundImages={backgroundImages}
        showLogo={false}
      >
        {/* Main content container */}
        <div className="w-full max-w-[100rem] mx-auto mt-[6rem] mb-[2rem]">
          {/* Title */}
          <h1
            className="text-center text-white mb-12 font-rubik font-medium capitalize"
            style={{
              fontSize: '40px',
              lineHeight: '100%',
              letterSpacing: '0%',
            }}
          >
            Select Your Sport
          </h1>

          {/* Loading State */}
          {isLoading && (
            <div className="text-center text-white">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--golden)]"></div>
              <p className="mt-4">Loading sports categories...</p>
            </div>
          )}

          {/* Error State */}
          {isError && (
            <div className="text-center">
              <div className="bg-red-500/10 border border-red-500/30 text-red-400 px-6 py-4 rounded-lg max-w-md mx-auto">
                {error?.message || 'Failed to load sports categories. Please try again.'}
              </div>
            </div>
          )}

          {/* Sports Categories Grid */}
          {categoriesData?.data?.availableDisciplines && (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 xl:grid-cols-7 2xl:grid-cols-10 gap-6 justify-items-center">
              {categoriesData.data.availableDisciplines.map((discipline) => (
                <SportsCategoryCard
                  key={discipline.id}
                  discipline={discipline}
                  onClick={handleCategorySelect}
                />
              ))}
            </div>
          )}
        </div>
      </AuthenticationBackgroundWrapper>
    </Fragment>
  );
}
