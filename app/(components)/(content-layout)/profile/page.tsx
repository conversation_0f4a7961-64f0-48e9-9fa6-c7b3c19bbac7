// app/(components)/(content-layout)/profile/page.tsx - User Profile Page
import React from "react";
import { Metadata } from "next";
import { ProfilePageClient } from "./components/ProfilePageClient";
import { 
    BreadcrumbStructuredData, 
    SoftwareApplicationStructuredData 
} from "@/shared/seo/components/StructuredData";

// Generate metadata for the profile page
export const metadata: Metadata = {
    title: "User Profile | Xintra",
    description: "View and manage your user profile information, account details, and settings",
    keywords: ["profile", "user account", "personal information", "account settings"],
    openGraph: {
        title: "User Profile | Xintra",
        description: "View and manage your user profile information, account details, and settings",
        type: "website",
    },
    twitter: {
        card: "summary",
        title: "User Profile | Xintra",
        description: "View and manage your user profile information, account details, and settings",
    },
};

/**
 * Server-side rendered user profile page component
 * Handles SEO metadata generation and delegates client-side logic to ProfilePageClient
 */
const ProfilePage: React.FC = () => {
    return (
        <>
            {/* Structured data for SEO */}
            <BreadcrumbStructuredData
                breadcrumbs={[
                    { name: "Home", url: "/" },
                    { name: "Profile" }
                ]}
            />
            <SoftwareApplicationStructuredData
                name="User Profile Management"
                description="Comprehensive user profile management with personal information, account details, and settings"
                url="/profile"
                applicationCategory="BusinessApplication"
                operatingSystem="Web Browser"
            />
            
            {/* Client-side component handles all interactive functionality */}
            <ProfilePageClient />
        </>
    );
};

export default ProfilePage;
