// app/(components)/(content-layout)/profile/components/ProfileDetailsSection.tsx - Profile details section component
"use client";

import React from "react";
import SpkButton from "@/shared/@spk-reusable-components/uielements/spk-button";
import { User } from "@/shared/types/global";

export interface ProfileDetailsSectionProps {
    user: User | null;
    onChangePassword: () => void;
    className?: string;
}

/**
 * Profile details section component that displays user information
 * Shows all relevant user information from LoginResponse interface
 * 
 * Features:
 * - First Name, Last Name display
 * - Mobile Number, Email ID display
 * - Agent Name display
 * - Internal Player ID display
 * - Created At timestamp display
 * - Change Password button
 * - Responsive design
 * - Consistent styling with design system
 */
export const ProfileDetailsSection: React.FC<ProfileDetailsSectionProps> = ({
    user,
    onChangePassword,
    className = ""
}) => {
    // Format the created at timestamp
    const formatCreatedAt = (timestamp: string) => {
        try {
            const date = new Date(timestamp);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch {
            return 'N/A';
        }
    };

    // Profile detail item component for consistent styling
    const ProfileDetailItem: React.FC<{
        label: string;
        value: string | number | undefined;
        icon?: string;
    }> = ({ label, value, icon }) => (
        <div className="flex flex-col item-center gap-[2px] sm:flex-row sm:items-center sm:justify-between p-4 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
            <div className="flex items-center gap-3 mb-2 sm:mb-0">
                {icon && <i className={`${icon} text-primary text-lg`}></i>}
                <span className="font-medium text-gray-700 dark:text-gray-300">{label}:</span>
            </div>
            <div className="text-gray-900 dark:text-white font-semibold">
                {value || 'N/A'}
            </div>
        </div>
    );

    return (
        <div className={`bg-nav rounded-md shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
            {/* Section Header */}
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                    <i className="ri-user-settings-line text-primary"></i>
                    Profile Details
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    View and manage your personal information
                </p>
            </div>

            {/* Profile Details Content */}
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {/* First Name */}
                <ProfileDetailItem
                    label="First Name"
                    value={user?.first_name}
                    icon="ri-user-line"
                />

                {/* Last Name */}
                <ProfileDetailItem
                    label="Last Name"
                    value={user?.last_name}
                    icon="ri-user-line"
                />

                {/* Mobile Number */}
                <ProfileDetailItem
                    label="Mobile Number"
                    value={user?.phone}
                    icon="ri-phone-line"
                />

                {/* Email ID */}
                <ProfileDetailItem
                    label="Email ID"
                    value={user?.email}
                    icon="ri-mail-line"
                />

                {/* Agent Name */}
                <ProfileDetailItem
                    label="Agent Name"
                    value={user?.agent_name}
                    icon="ri-user-star-line"
                />

                {/* Internal Player ID */}
                <ProfileDetailItem
                    label="Internal Player ID"
                    value={user?.internalPlayerID}
                    icon="ri-hashtag"
                />

                {/* Created At */}
                <ProfileDetailItem
                    label="Created At"
                    value={user?.created_at ? formatCreatedAt(user.created_at) : 'N/A'}
                    icon="ri-calendar-line"
                />

                {/* Change Password Action */}
                <div className="p-4">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <div className="flex items-center gap-3 mb-3 sm:mb-0">
                            <i className="ri-lock-password-line text-primary text-lg"></i>
                            <div>
                                <span className="font-medium text-gray-700 dark:text-gray-300 block">Password</span>
                                <span className="text-sm text-gray-500 dark:text-gray-400">
                                    Update your account password
                                </span>
                            </div>
                        </div>
                        <SpkButton
                            variant="primary"
                            Size="sm"
                            onclickfunc={onChangePassword}
                            customClass="flex items-center gap-2"
                        >
                            <i className="ri-key-line text-sm"></i>
                            Change Password
                        </SpkButton>
                    </div>
                </div>
            </div>
        </div>
    );
};
