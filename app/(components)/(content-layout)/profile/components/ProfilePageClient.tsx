// app/(components)/(content-layout)/profile/components/ProfilePageClient.tsx - Client-side component for user profile
"use client";

import React, { Fragment } from "react";
import { useRouter } from "next/navigation";
import fadeInStyles from '@/app/css/animations/fade-in.module.css';
import { useProfilePageLogic } from "@/shared/hooks/business/useProfilePageLogic";
import { CardSkeleton, SpkErrorMessage } from "@/shared/UI/components";
import { ProfileHeader } from "./ProfileHeader";
import { ProfileDetailsSection } from "./ProfileDetailsSection";

/**
 * Client-side component that handles all interactive functionality for user profile
 * Displays user profile information from LoginResponse interface
 * 
 * Layout structure:
 * 1. Profile Header (similar to user-details-page header)
 * 2. Profile Details Section (user information and actions)
 */
export function ProfilePageClient() {
    const router = useRouter();

    // Use custom hook for all business logic
    const {
        user,
        isLoading,
        isError,
        error,
        isAuthenticated,
        hasHydrated,
        availableBalance,
        lastDepositedAmount,
        walletBalance,
        handleChangePassword,
        handleRefresh
    } = useProfilePageLogic();

    // Show loading skeleton while hydrating or loading
    if (!hasHydrated || isLoading) {
        return (
            <div className={`flex flex-col gap-5 ${fadeInStyles.fadeIn}`}>
                <CardSkeleton className="h-[144px]" />
                <CardSkeleton className="h-[400px]" />
            </div>
        );
    }

    // Redirect to login if not authenticated
    if (!isAuthenticated) {
        router.replace("/authentication/sign-in/");
        return null;
    }

    // Show error state
    if (isError || !user) {
        return (
            <div className={`flex flex-col gap-5 ${fadeInStyles.fadeIn}`}>
                <SpkErrorMessage
                    title="Profile Load Error"
                    message={error?.message || "Failed to load profile information. Please try again."}
                    onRetry={handleRefresh}
                />
            </div>
        );
    }

    return (
        <Fragment>
            {/* Main Content with Profile Layout */}
            <div className={`flex flex-col gap-5 ${fadeInStyles.fadeIn}`}>
                {/* Profile Header Section */}
                <ProfileHeader
                    user={user}
                    availableBalance={availableBalance}
                    lastDepositedAmount={lastDepositedAmount}
                    walletBalance={walletBalance}
                />

                {/* Profile Details Section */}
                <ProfileDetailsSection
                    user={user}
                    onChangePassword={handleChangePassword}
                />
            </div>
        </Fragment>
    );
}
