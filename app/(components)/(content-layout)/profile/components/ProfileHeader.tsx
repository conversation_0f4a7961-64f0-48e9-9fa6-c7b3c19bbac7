// app/(components)/(content-layout)/profile/components/ProfileHeader.tsx - Profile header component
"use client";

import React from "react";
import Image from "next/image";
import { isExternalImage } from "@/shared/utils/imageOptimization";
import { CurrencyDisplay } from "@/shared/UI/components";

export interface ProfileHeaderProps {
    user: any;
    availableBalance?: number;
    lastDepositedAmount?: number;
    walletBalance?: number;
    className?: string;
}

/**
 * Profile header component that displays user profile information
 * Reuses the exact header design from user-details-page
 * 
 * Features:
 * - Profile image with fallback handling
 * - Available Balance display
 * - Last Deposited Amount display
 * - Last Updated At timestamp
 * - Wallet balance display
 * - Responsive design (mobile/desktop layouts)
 * - Dark theme compatible
 * - Consistent styling with design system
 */
export const ProfileHeader: React.FC<ProfileHeaderProps> = ({
    user,
    availableBalance = 0,
    lastDepositedAmount = 0,
    walletBalance = 0,
    className = ""
}) => {
    // Format the last updated timestamp
    const formatLastUpdated = (timestamp: string) => {
        try {
            const date = new Date(timestamp);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch {
            return 'N/A';
        }
    };

    // Get user display name
    const userName = user ? `${user.first_name || ''} ${user.last_name || ''}`.trim() || user.email || 'User' : 'User';

    return (
        <div className={`h-auto lg:h-[144px] bg-profile-gradient rounded-[1rem] px-3 py-4 flex flex-col lg:flex-row justify-between gap-4 ${className}`}>
            {/* Left Section: Profile Info */}
            <div className="flex items-center gap-4">
                {/* Profile Image */}
                <div className="w-20 h-20 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden">
                    {(() => {
                        // For now, use default profile image
                        // This can be enhanced to use user's actual avatar
                        const avatarUrl = '/assets/images/profile.png';
                        const isDefaultFallback = true;

                        return !isDefaultFallback ? (
                            <div className="relative w-full h-full">
                                <Image
                                    src={avatarUrl}
                                    alt="User Avatar"
                                    fill
                                    className="object-cover"
                                    unoptimized={isExternalImage(avatarUrl)}
                                    onError={(e) => {
                                        const target = e.target as HTMLImageElement;
                                        target.style.display = 'none';
                                        (e.currentTarget.parentNode?.nextSibling as HTMLElement)?.classList.remove('hidden');
                                    }}
                                />
                            </div>
                        ) : null;
                    })()}
                    <i className="ri-user-line text-2xl text-gray-600"></i>
                </div>

                {/* User Details */}
                <div className="flex flex-col gap-2">
                    {/* Username */}
                    <h2 className="text-xl font-semibold text-white font-rubik">
                        {userName}
                    </h2>

                    {/* User Info */}
                    <div className="flex flex-col gap-1 text-sm text-gray-300">
                        <div className="flex items-center gap-2">
                            <i className="ri-mail-line text-sm"></i>
                            <span>{user?.email || 'N/A'}</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <i className="ri-phone-line text-sm"></i>
                            <span>{user?.phone || 'N/A'}</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <i className="ri-calendar-line text-sm"></i>
                            <span>Joined: {user?.created_at ? formatLastUpdated(user.created_at) : 'N/A'}</span>
                        </div>
                    </div>
                </div>
            </div>

            {/* Right Section: Balance Information */}
            <div className="flex flex-col lg:flex-row gap-4 lg:items-center">
                {/* Available Balance */}
                <div className="flex flex-col items-center lg:items-end">
                    <div className="text-sm text-gray-300 font-rubik mb-1">Available Balance</div>
                    <div className="text-xl font-semibold text-white font-rubik">
                        <CurrencyDisplay
                            amount={availableBalance}
                            context="header"
                            size={20}
                            amountClassName="text-xl font-semibold font-rubik"
                            gap="sm"
                        />
                    </div>
                </div>

                {/* Last Deposited Amount */}
                <div className="flex flex-col items-center lg:items-end">
                    <div className="text-sm text-gray-300 font-rubik mb-1">Last Deposited Amount</div>
                    <div className="text-xl font-semibold text-white font-rubik">
                        <CurrencyDisplay
                            amount={lastDepositedAmount}
                            context="header"
                            size={20}
                            amountClassName="text-xl font-semibold font-rubik"
                            gap="sm"
                        />
                    </div>
                </div>

                {/* Last Updated At */}
                <div className="flex flex-col items-center lg:items-end">
                    <div className="text-sm text-gray-300 font-rubik mb-1">Last Updated At</div>
                    <div className="text-sm text-white font-rubik">
                        {user?.updated_at ? formatLastUpdated(user.updated_at) : 'N/A'}
                    </div>
                </div>

                {/* Wallet Balance */}
                <div className="flex flex-col bg-golden-button justify-center items-center p-[1px] h-full w-[250px] border-none rounded-lg overflow-hidden relative min-h-[80px]">
                    <div className="flex flex-col justify-center items-center text-center m-[1px] h-full w-full rounded-[12px] bg-secondary-btn">
                        <div className="text-2xl font-semibold text-white font-rubik">
                            <CurrencyDisplay
                                amount={walletBalance}
                                context="header"
                                size={20}
                                amountClassName="text-2xl font-semibold font-rubik"
                                gap="sm"
                            />
                        </div>
                        <div className="text-sm text-gray-300 font-rubik">
                            Wallet Balance
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};
